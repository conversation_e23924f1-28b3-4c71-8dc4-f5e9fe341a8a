-- سكريبت إنشاء قاعدة البيانات ونظام ERP
-- تاريخ الإنشاء: 2025-06-26

-- إن<PERSON>اء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS `erp_system` 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE `erp_system`;

-- جدول المستخدمين
CREATE TABLE IF NOT EXISTS `users` (
    `user_id` INT AUTO_INCREMENT PRIMARY KEY,
    `username` VARCHAR(50) NOT NULL UNIQUE,
    `password_hash` VARCHAR(255) NOT NULL,
    `full_name` VARCHAR(100) NOT NULL,
    `email` VARCHAR(100) UNIQUE,
    `phone` VARCHAR(20),
    `role` ENUM('admin', 'accountant', 'hr_manager', 'employee', 'viewer') NOT NULL DEFAULT 'employee',
    `is_active` <PERSON><PERSON><PERSON><PERSON>N NOT NULL DEFAULT TRUE,
    `last_login` <PERSON><PERSON><PERSON><PERSON><PERSON> NULL,
    `failed_login_attempts` INT DEFAULT 0,
    `password_changed_at` DATETIME NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_username` (`username`),
    INDEX `idx_email` (`email`),
    INDEX `idx_role` (`role`)
) ENGINE=InnoDB;

-- جدول الحسابات المحاسبية
CREATE TABLE IF NOT EXISTS `accounts` (
    `account_id` INT AUTO_INCREMENT PRIMARY KEY,
    `account_code` VARCHAR(20) NOT NULL UNIQUE,
    `account_name` VARCHAR(100) NOT NULL,
    `account_type` ENUM('asset', 'liability', 'equity', 'revenue', 'expense') NOT NULL,
    `parent_account_id` INT NULL,
    `balance` DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    `is_active` BOOLEAN NOT NULL DEFAULT TRUE,
    `description` TEXT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`parent_account_id`) REFERENCES `accounts`(`account_id`) ON DELETE SET NULL,
    INDEX `idx_account_code` (`account_code`),
    INDEX `idx_account_type` (`account_type`),
    INDEX `idx_parent_account` (`parent_account_id`)
) ENGINE=InnoDB;

-- جدول العملاء والموردين
CREATE TABLE IF NOT EXISTS `customers_suppliers` (
    `entity_id` INT AUTO_INCREMENT PRIMARY KEY,
    `entity_code` VARCHAR(20) NOT NULL UNIQUE,
    `entity_name` VARCHAR(100) NOT NULL,
    `entity_type` ENUM('customer', 'supplier', 'both') NOT NULL,
    `contact_person` VARCHAR(100) NULL,
    `phone` VARCHAR(20) NULL,
    `email` VARCHAR(100) NULL,
    `address` TEXT NULL,
    `city` VARCHAR(50) NULL,
    `country` VARCHAR(50) NULL,
    `tax_number` VARCHAR(50) NULL,
    `credit_limit` DECIMAL(15,2) DEFAULT 0.00,
    `current_balance` DECIMAL(15,2) DEFAULT 0.00,
    `is_active` BOOLEAN NOT NULL DEFAULT TRUE,
    `notes` TEXT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_entity_code` (`entity_code`),
    INDEX `idx_entity_type` (`entity_type`),
    INDEX `idx_entity_name` (`entity_name`)
) ENGINE=InnoDB;

-- جدول الفواتير
CREATE TABLE IF NOT EXISTS `invoices` (
    `invoice_id` INT AUTO_INCREMENT PRIMARY KEY,
    `invoice_number` VARCHAR(50) NOT NULL UNIQUE,
    `invoice_type` ENUM('sales', 'purchase', 'return_sales', 'return_purchase') NOT NULL,
    `customer_supplier_id` INT NOT NULL,
    `invoice_date` DATE NOT NULL,
    `due_date` DATE NULL,
    `subtotal` DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    `tax_amount` DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    `discount_amount` DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    `total_amount` DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    `paid_amount` DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    `status` ENUM('draft', 'pending', 'paid', 'partially_paid', 'overdue', 'cancelled') NOT NULL DEFAULT 'draft',
    `payment_terms` VARCHAR(100) NULL,
    `notes` TEXT NULL,
    `created_by` INT NOT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`customer_supplier_id`) REFERENCES `customers_suppliers`(`entity_id`) ON DELETE RESTRICT,
    FOREIGN KEY (`created_by`) REFERENCES `users`(`user_id`) ON DELETE RESTRICT,
    INDEX `idx_invoice_number` (`invoice_number`),
    INDEX `idx_invoice_type` (`invoice_type`),
    INDEX `idx_invoice_date` (`invoice_date`),
    INDEX `idx_status` (`status`)
) ENGINE=InnoDB;

-- جدول بنود الفواتير
CREATE TABLE IF NOT EXISTS `invoice_items` (
    `item_id` INT AUTO_INCREMENT PRIMARY KEY,
    `invoice_id` INT NOT NULL,
    `item_description` VARCHAR(255) NOT NULL,
    `quantity` DECIMAL(10,3) NOT NULL DEFAULT 1.000,
    `unit_price` DECIMAL(15,2) NOT NULL,
    `total_price` DECIMAL(15,2) NOT NULL,
    `tax_rate` DECIMAL(5,2) NOT NULL DEFAULT 0.00,
    `tax_amount` DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    `discount_rate` DECIMAL(5,2) NOT NULL DEFAULT 0.00,
    `discount_amount` DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    `notes` TEXT NULL,
    FOREIGN KEY (`invoice_id`) REFERENCES `invoices`(`invoice_id`) ON DELETE CASCADE,
    INDEX `idx_invoice_id` (`invoice_id`)
) ENGINE=InnoDB;

-- جدول الموظفين
CREATE TABLE IF NOT EXISTS `employees` (
    `employee_id` INT AUTO_INCREMENT PRIMARY KEY,
    `employee_code` VARCHAR(20) NOT NULL UNIQUE,
    `user_id` INT NULL,
    `full_name` VARCHAR(100) NOT NULL,
    `position` VARCHAR(100) NULL,
    `department` VARCHAR(100) NULL,
    `hire_date` DATE NOT NULL,
    `birth_date` DATE NULL,
    `national_id` VARCHAR(50) NULL,
    `passport_number` VARCHAR(50) NULL,
    `phone` VARCHAR(20) NULL,
    `email` VARCHAR(100) NULL,
    `address` TEXT NULL,
    `emergency_contact` VARCHAR(100) NULL,
    `emergency_phone` VARCHAR(20) NULL,
    `basic_salary` DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    `allowances` DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    `bank_account` VARCHAR(50) NULL,
    `bank_name` VARCHAR(100) NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT TRUE,
    `termination_date` DATE NULL,
    `notes` TEXT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`user_id`) ON DELETE SET NULL,
    INDEX `idx_employee_code` (`employee_code`),
    INDEX `idx_department` (`department`),
    INDEX `idx_hire_date` (`hire_date`)
) ENGINE=InnoDB;

-- جدول القيود المحاسبية
CREATE TABLE IF NOT EXISTS `journal_entries` (
    `entry_id` INT AUTO_INCREMENT PRIMARY KEY,
    `entry_number` VARCHAR(50) NOT NULL UNIQUE,
    `entry_date` DATE NOT NULL,
    `description` TEXT NOT NULL,
    `reference_type` ENUM('manual', 'invoice', 'payment', 'salary', 'adjustment') NOT NULL DEFAULT 'manual',
    `reference_id` INT NULL,
    `total_debit` DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    `total_credit` DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    `status` ENUM('draft', 'posted', 'cancelled') NOT NULL DEFAULT 'draft',
    `created_by` INT NOT NULL,
    `posted_by` INT NULL,
    `posted_at` DATETIME NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`created_by`) REFERENCES `users`(`user_id`) ON DELETE RESTRICT,
    FOREIGN KEY (`posted_by`) REFERENCES `users`(`user_id`) ON DELETE RESTRICT,
    INDEX `idx_entry_number` (`entry_number`),
    INDEX `idx_entry_date` (`entry_date`),
    INDEX `idx_reference` (`reference_type`, `reference_id`),
    INDEX `idx_status` (`status`)
) ENGINE=InnoDB;

-- جدول تفاصيل القيود المحاسبية
CREATE TABLE IF NOT EXISTS `journal_entry_details` (
    `detail_id` INT AUTO_INCREMENT PRIMARY KEY,
    `entry_id` INT NOT NULL,
    `account_id` INT NOT NULL,
    `description` VARCHAR(255) NULL,
    `debit_amount` DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    `credit_amount` DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    FOREIGN KEY (`entry_id`) REFERENCES `journal_entries`(`entry_id`) ON DELETE CASCADE,
    FOREIGN KEY (`account_id`) REFERENCES `accounts`(`account_id`) ON DELETE RESTRICT,
    INDEX `idx_entry_id` (`entry_id`),
    INDEX `idx_account_id` (`account_id`)
) ENGINE=InnoDB;

-- جدول المعاملات المالية
CREATE TABLE IF NOT EXISTS `financial_transactions` (
    `transaction_id` INT AUTO_INCREMENT PRIMARY KEY,
    `transaction_type` ENUM('payment', 'receipt', 'transfer', 'adjustment') NOT NULL,
    `transaction_number` VARCHAR(50) NOT NULL UNIQUE,
    `from_account_id` INT NULL,
    `to_account_id` INT NULL,
    `amount` DECIMAL(15,2) NOT NULL,
    `description` TEXT NULL,
    `transaction_date` DATE NOT NULL,
    `reference_number` VARCHAR(100) NULL,
    `payment_method` ENUM('cash', 'bank_transfer', 'check', 'credit_card', 'other') NULL,
    `status` ENUM('pending', 'completed', 'cancelled') NOT NULL DEFAULT 'pending',
    `created_by` INT NOT NULL,
    `approved_by` INT NULL,
    `approved_at` DATETIME NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`from_account_id`) REFERENCES `accounts`(`account_id`) ON DELETE RESTRICT,
    FOREIGN KEY (`to_account_id`) REFERENCES `accounts`(`account_id`) ON DELETE RESTRICT,
    FOREIGN KEY (`created_by`) REFERENCES `users`(`user_id`) ON DELETE RESTRICT,
    FOREIGN KEY (`approved_by`) REFERENCES `users`(`user_id`) ON DELETE RESTRICT,
    INDEX `idx_transaction_number` (`transaction_number`),
    INDEX `idx_transaction_date` (`transaction_date`),
    INDEX `idx_transaction_type` (`transaction_type`),
    INDEX `idx_status` (`status`)
) ENGINE=InnoDB;

-- إدراج البيانات الأولية

-- إدراج المستخدم الافتراضي (admin)
-- كلمة المرور: admin123 (مشفرة بـ BCrypt)
INSERT INTO `users` (`username`, `password_hash`, `full_name`, `email`, `role`, `is_active`)
VALUES ('admin', '$2a$11$rQiU9k7Z8QqZ9k7Z8QqZ9O7Z8QqZ9k7Z8QqZ9k7Z8QqZ9k7Z8QqZ9k', 'مدير النظام', '<EMAIL>', 'admin', TRUE)
ON DUPLICATE KEY UPDATE `updated_at` = CURRENT_TIMESTAMP;

-- إدراج الحسابات الأساسية
INSERT INTO `accounts` (`account_code`, `account_name`, `account_type`, `parent_account_id`, `is_active`) VALUES
('1000', 'الأصول', 'asset', NULL, TRUE),
('1100', 'الأصول المتداولة', 'asset', 1, TRUE),
('1110', 'النقدية والبنوك', 'asset', 2, TRUE),
('1111', 'الصندوق', 'asset', 3, TRUE),
('1112', 'البنك الأهلي', 'asset', 3, TRUE),
('1120', 'العملاء', 'asset', 2, TRUE),
('1130', 'المخزون', 'asset', 2, TRUE),
('1200', 'الأصول الثابتة', 'asset', 1, TRUE),
('1210', 'الأثاث والمعدات', 'asset', 8, TRUE),
('1220', 'السيارات', 'asset', 8, TRUE),

('2000', 'الخصوم', 'liability', NULL, TRUE),
('2100', 'الخصوم المتداولة', 'liability', 11, TRUE),
('2110', 'الموردون', 'liability', 12, TRUE),
('2120', 'المصروفات المستحقة', 'liability', 12, TRUE),
('2200', 'الخصوم طويلة الأجل', 'liability', 11, TRUE),
('2210', 'القروض طويلة الأجل', 'liability', 15, TRUE),

('3000', 'حقوق الملكية', 'equity', NULL, TRUE),
('3100', 'رأس المال', 'equity', 17, TRUE),
('3200', 'الأرباح المحتجزة', 'equity', 17, TRUE),

('4000', 'الإيرادات', 'revenue', NULL, TRUE),
('4100', 'إيرادات المبيعات', 'revenue', 20, TRUE),
('4200', 'إيرادات أخرى', 'revenue', 20, TRUE),

('5000', 'المصروفات', 'expense', NULL, TRUE),
('5100', 'تكلفة البضاعة المباعة', 'expense', 23, TRUE),
('5200', 'مصروفات التشغيل', 'expense', 23, TRUE),
('5210', 'الرواتب والأجور', 'expense', 25, TRUE),
('5220', 'الإيجار', 'expense', 25, TRUE),
('5230', 'الكهرباء والماء', 'expense', 25, TRUE),
('5240', 'الاتصالات', 'expense', 25, TRUE),
('5250', 'مصروفات أخرى', 'expense', 25, TRUE)
ON DUPLICATE KEY UPDATE `updated_at` = CURRENT_TIMESTAMP;
