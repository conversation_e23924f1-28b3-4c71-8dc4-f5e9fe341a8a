using MySql.Data.MySqlClient;
using Serilog;
using System.Data;

namespace erp.Data
{
    /// <summary>
    /// فئة مساعدة لعمليات قاعدة البيانات الشائعة
    /// </summary>
    public static class DatabaseHelper
    {
        /// <summary>
        /// إنشاء قاعدة البيانات إذا لم تكن موجودة
        /// </summary>
        /// <param name="connectionString">سلسلة الاتصال</param>
        /// <returns>true إذا تم الإنشاء بنجاح</returns>
        public static async Task<bool> CreateDatabaseIfNotExistsAsync(string connectionString)
        {
            try
            {
                // استخراج اسم قاعدة البيانات من سلسلة الاتصال
                var builder = new MySqlConnectionStringBuilder(connectionString);
                var databaseName = builder.Database;
                
                // إنشاء سلسلة اتصال بدون اسم قاعدة البيانات
                builder.Database = "";
                var connectionWithoutDb = builder.ConnectionString;

                using var connection = new MySqlConnection(connectionWithoutDb);
                await connection.OpenAsync();

                // التحقق من وجود قاعدة البيانات
                var checkQuery = $"SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '{databaseName}'";
                using var checkCommand = new MySqlCommand(checkQuery, connection);
                var result = await checkCommand.ExecuteScalarAsync();

                if (result == null)
                {
                    // إنشاء قاعدة البيانات
                    var createQuery = $"CREATE DATABASE `{databaseName}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
                    using var createCommand = new MySqlCommand(createQuery, connection);
                    await createCommand.ExecuteNonQueryAsync();
                    
                    Log.Information("تم إنشاء قاعدة البيانات {DatabaseName} بنجاح", databaseName);
                    return true;
                }
                else
                {
                    Log.Information("قاعدة البيانات {DatabaseName} موجودة بالفعل", databaseName);
                    return true;
                }
            }
            catch (Exception ex)
            {
                Log.Error("خطأ في إنشاء قاعدة البيانات: {Error}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// تنفيذ سكريبت SQL من ملف
        /// </summary>
        /// <param name="connectionString">سلسلة الاتصال</param>
        /// <param name="scriptPath">مسار ملف السكريبت</param>
        /// <returns>true إذا تم التنفيذ بنجاح</returns>
        public static async Task<bool> ExecuteSqlScriptAsync(string connectionString, string scriptPath)
        {
            try
            {
                if (!File.Exists(scriptPath))
                {
                    Log.Error("ملف السكريبت غير موجود: {ScriptPath}", scriptPath);
                    return false;
                }

                var script = await File.ReadAllTextAsync(scriptPath);
                var statements = script.Split(new[] { ";" }, StringSplitOptions.RemoveEmptyEntries);

                using var connection = new MySqlConnection(connectionString);
                await connection.OpenAsync();

                foreach (var statement in statements)
                {
                    var trimmedStatement = statement.Trim();
                    if (!string.IsNullOrEmpty(trimmedStatement))
                    {
                        using var command = new MySqlCommand(trimmedStatement, connection);
                        await command.ExecuteNonQueryAsync();
                    }
                }

                Log.Information("تم تنفيذ سكريبت SQL بنجاح: {ScriptPath}", scriptPath);
                return true;
            }
            catch (Exception ex)
            {
                Log.Error("خطأ في تنفيذ سكريبت SQL: {ScriptPath}, خطأ: {Error}", scriptPath, ex.Message);
                return false;
            }
        }

        /// <summary>
        /// التحقق من وجود جدول في قاعدة البيانات
        /// </summary>
        /// <param name="connectionString">سلسلة الاتصال</param>
        /// <param name="tableName">اسم الجدول</param>
        /// <returns>true إذا كان الجدول موجوداً</returns>
        public static async Task<bool> TableExistsAsync(string connectionString, string tableName)
        {
            try
            {
                using var connection = new MySqlConnection(connectionString);
                await connection.OpenAsync();

                var query = "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = @tableName";
                using var command = new MySqlCommand(query, connection);
                command.Parameters.AddWithValue("@tableName", tableName);

                var result = await command.ExecuteScalarAsync();
                return Convert.ToInt32(result) > 0;
            }
            catch (Exception ex)
            {
                Log.Error("خطأ في التحقق من وجود الجدول {TableName}: {Error}", tableName, ex.Message);
                return false;
            }
        }

        /// <summary>
        /// إنشاء نسخة احتياطية من قاعدة البيانات
        /// </summary>
        /// <param name="connectionString">سلسلة الاتصال</param>
        /// <param name="backupPath">مسار النسخة الاحتياطية</param>
        /// <returns>true إذا تم إنشاء النسخة الاحتياطية بنجاح</returns>
        public static async Task<bool> CreateBackupAsync(string connectionString, string backupPath)
        {
            try
            {
                var builder = new MySqlConnectionStringBuilder(connectionString);
                var databaseName = builder.Database;
                
                // إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
                var backupDirectory = Path.GetDirectoryName(backupPath);
                if (!string.IsNullOrEmpty(backupDirectory) && !Directory.Exists(backupDirectory))
                {
                    Directory.CreateDirectory(backupDirectory);
                }

                // تنفيذ أمر mysqldump (يتطلب وجود MySQL في PATH)
                var fileName = $"backup_{databaseName}_{DateTime.Now:yyyyMMdd_HHmmss}.sql";
                var fullBackupPath = Path.Combine(backupDirectory ?? "", fileName);

                // هذا مثال بسيط - في الواقع قد تحتاج لاستخدام MySqlBackup library أو تنفيذ mysqldump
                Log.Information("تم طلب إنشاء نسخة احتياطية: {BackupPath}", fullBackupPath);
                
                // TODO: تنفيذ عملية النسخ الاحتياطي الفعلية
                return true;
            }
            catch (Exception ex)
            {
                Log.Error("خطأ في إنشاء النسخة الاحتياطية: {Error}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// تحويل DataReader إلى قائمة من Dictionary
        /// </summary>
        /// <param name="reader">DataReader</param>
        /// <returns>قائمة من Dictionary تحتوي على البيانات</returns>
        public static async Task<List<Dictionary<string, object>>> DataReaderToListAsync(MySqlDataReader reader)
        {
            var result = new List<Dictionary<string, object>>();

            try
            {
                while (await reader.ReadAsync())
                {
                    var row = new Dictionary<string, object>();
                    for (int i = 0; i < reader.FieldCount; i++)
                    {
                        var columnName = reader.GetName(i);
                        var value = reader.IsDBNull(i) ? null : reader.GetValue(i);
                        row[columnName] = value;
                    }
                    result.Add(row);
                }
            }
            catch (Exception ex)
            {
                Log.Error("خطأ في تحويل DataReader إلى List: {Error}", ex.Message);
                throw;
            }

            return result;
        }

        /// <summary>
        /// إنشاء معاملات MySQL من كائن
        /// </summary>
        /// <param name="parameters">كائن يحتوي على المعاملات</param>
        /// <returns>مصفوفة من MySqlParameter</returns>
        public static MySqlParameter[] CreateParameters(object parameters)
        {
            var parameterList = new List<MySqlParameter>();

            if (parameters != null)
            {
                var properties = parameters.GetType().GetProperties();
                foreach (var property in properties)
                {
                    var value = property.GetValue(parameters);
                    var parameter = new MySqlParameter($"@{property.Name}", value ?? DBNull.Value);
                    parameterList.Add(parameter);
                }
            }

            return parameterList.ToArray();
        }

        /// <summary>
        /// تنظيف وتحسين قاعدة البيانات
        /// </summary>
        /// <param name="connectionString">سلسلة الاتصال</param>
        /// <returns>true إذا تم التنظيف بنجاح</returns>
        public static async Task<bool> OptimizeDatabaseAsync(string connectionString)
        {
            try
            {
                using var connection = new MySqlConnection(connectionString);
                await connection.OpenAsync();

                // الحصول على قائمة الجداول
                var tablesQuery = "SHOW TABLES";
                using var tablesCommand = new MySqlCommand(tablesQuery, connection);
                using var tablesReader = await tablesCommand.ExecuteReaderAsync();

                var tables = new List<string>();
                while (await tablesReader.ReadAsync())
                {
                    tables.Add(tablesReader.GetString(0));
                }
                tablesReader.Close();

                // تحسين كل جدول
                foreach (var table in tables)
                {
                    var optimizeQuery = $"OPTIMIZE TABLE `{table}`";
                    using var optimizeCommand = new MySqlCommand(optimizeQuery, connection);
                    await optimizeCommand.ExecuteNonQueryAsync();
                }

                Log.Information("تم تحسين قاعدة البيانات بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                Log.Error("خطأ في تحسين قاعدة البيانات: {Error}", ex.Message);
                return false;
            }
        }
    }
}
