using MySql.Data.MySqlClient;
using Serilog;
using System.Configuration;

namespace erp.Data
{
    /// <summary>
    /// فئة إدارة الاتصال بقاعدة البيانات MySQL
    /// </summary>
    public class DatabaseConnection : IDisposable
    {
        private readonly string _connectionString;
        private MySqlConnection? _connection;
        private bool _disposed = false;

        /// <summary>
        /// منشئ فئة الاتصال بقاعدة البيانات
        /// </summary>
        public DatabaseConnection()
        {
            _connectionString = GetConnectionString();
            InitializeLogger();
        }

        /// <summary>
        /// منشئ فئة الاتصال بقاعدة البيانات مع سلسلة اتصال مخصصة
        /// </summary>
        /// <param name="connectionString">سلسلة الاتصال</param>
        public DatabaseConnection(string connectionString)
        {
            _connectionString = connectionString;
            InitializeLogger();
        }

        /// <summary>
        /// إعداد نظام التسجيل
        /// </summary>
        private void InitializeLogger()
        {
            Log.Logger = new LoggerConfiguration()
                .WriteTo.File("logs/erp-database-.txt", rollingInterval: RollingInterval.Day)
                .CreateLogger();
        }

        /// <summary>
        /// الحصول على سلسلة الاتصال من ملف التكوين أو القيم الافتراضية
        /// </summary>
        /// <returns>سلسلة الاتصال</returns>
        private string GetConnectionString()
        {
            try
            {
                // محاولة قراءة سلسلة الاتصال من ملف التكوين
                var connectionString = ConfigurationManager.ConnectionStrings["DefaultConnection"]?.ConnectionString;
                
                if (!string.IsNullOrEmpty(connectionString))
                {
                    return connectionString;
                }
            }
            catch (Exception ex)
            {
                Log.Warning("تعذر قراءة سلسلة الاتصال من ملف التكوين: {Error}", ex.Message);
            }

            // القيم الافتراضية لقاعدة البيانات المحلية
            return "Server=localhost;Database=erp_system;Uid=root;Pwd=;Charset=utf8mb4;";
        }

        /// <summary>
        /// فتح اتصال بقاعدة البيانات
        /// </summary>
        /// <returns>كائن الاتصال</returns>
        public async Task<MySqlConnection> OpenConnectionAsync()
        {
            try
            {
                if (_connection == null)
                {
                    _connection = new MySqlConnection(_connectionString);
                }

                if (_connection.State != System.Data.ConnectionState.Open)
                {
                    await _connection.OpenAsync();
                    Log.Information("تم فتح الاتصال بقاعدة البيانات بنجاح");
                }

                return _connection;
            }
            catch (MySqlException ex)
            {
                Log.Error("خطأ في الاتصال بقاعدة البيانات MySQL: {Error}", ex.Message);
                throw new DatabaseConnectionException($"فشل في الاتصال بقاعدة البيانات: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                Log.Error("خطأ عام في الاتصال بقاعدة البيانات: {Error}", ex.Message);
                throw new DatabaseConnectionException($"خطأ غير متوقع: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// فتح اتصال بقاعدة البيانات (نسخة متزامنة)
        /// </summary>
        /// <returns>كائن الاتصال</returns>
        public MySqlConnection OpenConnection()
        {
            try
            {
                if (_connection == null)
                {
                    _connection = new MySqlConnection(_connectionString);
                }

                if (_connection.State != System.Data.ConnectionState.Open)
                {
                    _connection.Open();
                    Log.Information("تم فتح الاتصال بقاعدة البيانات بنجاح");
                }

                return _connection;
            }
            catch (MySqlException ex)
            {
                Log.Error("خطأ في الاتصال بقاعدة البيانات MySQL: {Error}", ex.Message);
                throw new DatabaseConnectionException($"فشل في الاتصال بقاعدة البيانات: {ex.Message}", ex);
            }
            catch (Exception ex)
            {
                Log.Error("خطأ عام في الاتصال بقاعدة البيانات: {Error}", ex.Message);
                throw new DatabaseConnectionException($"خطأ غير متوقع: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// إغلاق الاتصال بقاعدة البيانات
        /// </summary>
        public void CloseConnection()
        {
            try
            {
                if (_connection != null && _connection.State == System.Data.ConnectionState.Open)
                {
                    _connection.Close();
                    Log.Information("تم إغلاق الاتصال بقاعدة البيانات");
                }
            }
            catch (Exception ex)
            {
                Log.Error("خطأ في إغلاق الاتصال بقاعدة البيانات: {Error}", ex.Message);
            }
        }

        /// <summary>
        /// اختبار الاتصال بقاعدة البيانات
        /// </summary>
        /// <returns>true إذا كان الاتصال ناجحاً</returns>
        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                using var connection = new MySqlConnection(_connectionString);
                await connection.OpenAsync();
                Log.Information("اختبار الاتصال بقاعدة البيانات نجح");
                return true;
            }
            catch (Exception ex)
            {
                Log.Error("فشل اختبار الاتصال بقاعدة البيانات: {Error}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// تنفيذ استعلام وإرجاع عدد الصفوف المتأثرة
        /// </summary>
        /// <param name="query">الاستعلام</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>عدد الصفوف المتأثرة</returns>
        public async Task<int> ExecuteNonQueryAsync(string query, MySqlParameter[]? parameters = null)
        {
            try
            {
                var connection = await OpenConnectionAsync();
                using var command = new MySqlCommand(query, connection);
                
                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters);
                }

                var result = await command.ExecuteNonQueryAsync();
                Log.Information("تم تنفيذ الاستعلام بنجاح. الصفوف المتأثرة: {RowsAffected}", result);
                return result;
            }
            catch (Exception ex)
            {
                Log.Error("خطأ في تنفيذ الاستعلام: {Query}, خطأ: {Error}", query, ex.Message);
                throw;
            }
        }

        /// <summary>
        /// تنفيذ استعلام وإرجاع قيمة واحدة
        /// </summary>
        /// <param name="query">الاستعلام</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>القيمة المرجعة</returns>
        public async Task<object?> ExecuteScalarAsync(string query, MySqlParameter[]? parameters = null)
        {
            try
            {
                var connection = await OpenConnectionAsync();
                using var command = new MySqlCommand(query, connection);
                
                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters);
                }

                var result = await command.ExecuteScalarAsync();
                Log.Information("تم تنفيذ استعلام Scalar بنجاح");
                return result;
            }
            catch (Exception ex)
            {
                Log.Error("خطأ في تنفيذ استعلام Scalar: {Query}, خطأ: {Error}", query, ex.Message);
                throw;
            }
        }

        /// <summary>
        /// تنفيذ استعلام وإرجاع DataReader
        /// </summary>
        /// <param name="query">الاستعلام</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>DataReader</returns>
        public async Task<MySqlDataReader> ExecuteReaderAsync(string query, MySqlParameter[]? parameters = null)
        {
            try
            {
                var connection = await OpenConnectionAsync();
                using var command = new MySqlCommand(query, connection);
                
                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters);
                }

                var reader = (MySqlDataReader)await command.ExecuteReaderAsync();
                Log.Information("تم تنفيذ استعلام Reader بنجاح");
                return reader;
            }
            catch (Exception ex)
            {
                Log.Error("خطأ في تنفيذ استعلام Reader: {Query}, خطأ: {Error}", query, ex.Message);
                throw;
            }
        }

        /// <summary>
        /// تحرير الموارد
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// تحرير الموارد المحمية
        /// </summary>
        /// <param name="disposing">هل يتم التحرير</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    CloseConnection();
                    _connection?.Dispose();
                }
                _disposed = true;
            }
        }

        /// <summary>
        /// المدمر
        /// </summary>
        ~DatabaseConnection()
        {
            Dispose(false);
        }
    }

    /// <summary>
    /// استثناء خاص بأخطاء الاتصال بقاعدة البيانات
    /// </summary>
    public class DatabaseConnectionException : Exception
    {
        public DatabaseConnectionException(string message) : base(message) { }
        public DatabaseConnectionException(string message, Exception innerException) : base(message, innerException) { }
    }
}
