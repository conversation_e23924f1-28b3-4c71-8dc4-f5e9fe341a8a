﻿namespace erp
{
    partial class Form1
    {
        /// <summary>
        ///  Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        private Panel sidebarPanel;
        private Button dashboardButton;
        private Button accountingSystemButton;
        private Button branchOperationsButton;
        private PictureBox accountingSystemIcon;

        /// <summary>
        ///  Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        ///  Required method for Designer support - do not modify
        ///  the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            sidebarPanel = new Panel();
            branchOperationsButton = new Button();
            accountingSystemButton = new Button();
            dashboardButton = new Button();
            accountingSystemIcon = new PictureBox();
            sidebarPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)accountingSystemIcon).BeginInit();
            SuspendLayout();
            // 
            // sidebarPanel
            // 
            sidebarPanel.BackColor = Color.LightGray;
            sidebarPanel.Controls.Add(branchOperationsButton);
            sidebarPanel.Controls.Add(accountingSystemButton);
            sidebarPanel.Controls.Add(dashboardButton);
            sidebarPanel.Dock = DockStyle.Left;
            sidebarPanel.Location = new Point(0, 0);
            sidebarPanel.Name = "sidebarPanel";
            sidebarPanel.Size = new Size(200, 657);
            sidebarPanel.TabIndex = 0;
            // 
            // branchOperationsButton
            // 
            branchOperationsButton.Dock = DockStyle.Top;
            branchOperationsButton.Location = new Point(0, 100);
            branchOperationsButton.Name = "branchOperationsButton";
            branchOperationsButton.Size = new Size(200, 50);
            branchOperationsButton.TabIndex = 0;
            branchOperationsButton.Text = "إدارة عمليات الفروع";
            // 
            // accountingSystemButton
            // 
            accountingSystemButton.Dock = DockStyle.Top;
            accountingSystemButton.Location = new Point(0, 50);
            accountingSystemButton.Name = "accountingSystemButton";
            accountingSystemButton.Size = new Size(200, 50);
            accountingSystemButton.TabIndex = 1;
            accountingSystemButton.Text = "نظام المحاسبة";
            accountingSystemButton.Click += accountingSystemButton_Click;
            // 
            // dashboardButton
            // 
            dashboardButton.Dock = DockStyle.Top;
            dashboardButton.Location = new Point(0, 0);
            dashboardButton.Name = "dashboardButton";
            dashboardButton.Size = new Size(200, 50);
            dashboardButton.TabIndex = 2;
            dashboardButton.Text = "لوحة القيادة";
            // 
            // accountingSystemIcon
            // 
            accountingSystemIcon.Cursor = Cursors.Hand;
            accountingSystemIcon.Location = new Point(100, 100);
            accountingSystemIcon.Name = "accountingSystemIcon";
            accountingSystemIcon.Size = new Size(100, 100);
            accountingSystemIcon.SizeMode = PictureBoxSizeMode.StretchImage;
            accountingSystemIcon.TabIndex = 1;
            accountingSystemIcon.TabStop = false;
            accountingSystemIcon.Click += AccountingSystemIcon_Click;
            // 
            // Form1
            // 
            AutoScaleDimensions = new SizeF(15F, 37F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1075, 657);
            Controls.Add(sidebarPanel);
            Controls.Add(accountingSystemIcon);
            Name = "Form1";
            Text = "الشاشة الرئيسية";
            sidebarPanel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)accountingSystemIcon).EndInit();
            ResumeLayout(false);
        }

        private void AccountingSystemIcon_Click(object sender, EventArgs e)
        {
            // Navigate to Accounting System page
            Form accountingForm = new AccountingForm();
            accountingForm.Show();
            this.Hide();
        }

        #endregion
    }
}
