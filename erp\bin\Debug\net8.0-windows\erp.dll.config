<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <connectionStrings>
        <!-- سلسلة الاتصال الافتراضية لقاعدة البيانات المحلية -->
        <add name="DefaultConnection" 
             connectionString="Server=localhost;Database=erp_system;Uid=root;Pwd=;Port=3306;Charset=utf8mb4;SslMode=none;AllowUserVariables=true;" 
             providerName="MySql.Data.MySqlClient" />
        
        <!-- سلسلة اتصال للإنتاج (يجب تحديثها حسب بيئة الإنتاج) -->
        <add name="ProductionConnection" 
             connectionString="Server=your_server;Database=erp_system;Uid=your_username;Pwd=your_password;Port=3306;Charset=utf8mb4;SslMode=Required;" 
             providerName="MySql.Data.MySqlClient" />
    </connectionStrings>
    
    <appSettings>
        <!-- إعدادات التطبيق العامة -->
        <add key="DatabaseTimeout" value="30" />
        <add key="LogLevel" value="Information" />
        <add key="EnableDatabaseLogging" value="true" />
        <add key="BackupPath" value="./Backups/" />
        <add key="MaxLoginAttempts" value="3" />
        <add key="SessionTimeout" value="30" />
        
        <!-- إعدادات النظام المحاسبي -->
        <add key="DefaultCurrency" value="SAR" />
        <add key="TaxRate" value="15" />
        <add key="FiscalYearStart" value="01-01" />
        
        <!-- إعدادات الأمان -->
        <add key="PasswordMinLength" value="8" />
        <add key="RequireSpecialCharacters" value="true" />
        <add key="PasswordExpiryDays" value="90" />
    </appSettings>
    
    <startup>
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
    </startup>
</configuration>
