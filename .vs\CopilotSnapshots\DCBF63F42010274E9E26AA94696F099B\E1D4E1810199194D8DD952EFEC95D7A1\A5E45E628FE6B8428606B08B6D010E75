﻿namespace erp
{
    public partial class MainScreen : Form
    {
        public MainScreen()
        {
            InitializeComponent();
        }

        private void accountingSystemButton_Click(object sender, EventArgs e)
        {
            AccountingForm accountingForm = new AccountingForm();
            accountingForm.Show();
            this.Hide();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            HRForm hrForm = new HRForm();
            hrForm.Show();
            this.Hide();
        }

        private void button2_Click(object sender, EventArgs e)
        {
            AccountingForm accountingForm = new AccountingForm();
            accountingForm.Show();
            this.Hide();
        }

        private void button3_Click(object sender, EventArgs e)
        {
            FinancialOperationsForm financialForm = new FinancialOperationsForm();
            financialForm.Show();
            this.Hide();
        }

        private void button4_Click(object sender, EventArgs e)
        {
            InternalMailForm mailForm = new InternalMailForm();
            mailForm.Show();
            this.Hide();
        }

        private void button5_Click(object sender, EventArgs e)
        {
            UserManagementForm userForm = new UserManagementForm();
            userForm.Show();
            this.Hide();
        }
    }
}
