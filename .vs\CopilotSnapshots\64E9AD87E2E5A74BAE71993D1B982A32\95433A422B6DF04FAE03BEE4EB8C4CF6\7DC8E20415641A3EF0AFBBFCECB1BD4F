﻿namespace erp
{
    public partial class AccountingForm : Form
    {
        private Button salesInvoicesButton;
        private Button purchasesInvoicesButton;
        private Button journalButton;
        private Button accountsButton;
        private Button financialReportsButton;
        private Button vatButton;
        private Label titleLabel;

        public AccountingForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            // Create buttons
            salesInvoicesButton = new Button();
            purchasesInvoicesButton = new Button();
            journalButton = new Button();
            accountsButton = new Button();
            financialReportsButton = new Button();
            vatButton = new Button();
            titleLabel = new Label();

            // Title Label
            titleLabel.Text = "نظام المحاسبة";
            titleLabel.AutoSize = true;
            titleLabel.Font = new Font("Segoe UI", 18F, FontStyle.Bold, GraphicsUnit.Point);
            titleLabel.Location = new Point(580, 30);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;

            // Sales Invoices Button
            salesInvoicesButton.Location = new Point(800, 150);
            salesInvoicesButton.Size = new Size(200, 150);
            salesInvoicesButton.Text = "فواتير البيع";
            salesInvoicesButton.Font = new Font("Segoe UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            salesInvoicesButton.Click += SalesInvoicesButton_Click;

            // Purchases Invoices Button
            purchasesInvoicesButton.Location = new Point(550, 150);
            purchasesInvoicesButton.Size = new Size(200, 150);
            purchasesInvoicesButton.Text = "فواتير مشتريات";
            purchasesInvoicesButton.Font = new Font("Segoe UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            purchasesInvoicesButton.Click += PurchasesInvoicesButton_Click;

            // Journal Button
            journalButton.Location = new Point(300, 150);
            journalButton.Size = new Size(200, 150);
            journalButton.Text = "دفتر اليومية";
            journalButton.Font = new Font("Segoe UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            journalButton.Click += JournalButton_Click;

            // Accounts Button
            accountsButton.Location = new Point(800, 350);
            accountsButton.Size = new Size(200, 150);
            accountsButton.Text = "الحسابات";
            accountsButton.Font = new Font("Segoe UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            accountsButton.Click += AccountsButton_Click;

            // Financial Reports Button
            financialReportsButton.Location = new Point(550, 350);
            financialReportsButton.Size = new Size(200, 150);
            financialReportsButton.Text = "التقارير المالية";
            financialReportsButton.Font = new Font("Segoe UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            financialReportsButton.Click += FinancialReportsButton_Click;

            // VAT Button
            vatButton.Location = new Point(300, 350);
            vatButton.Size = new Size(200, 150);
            vatButton.Text = "ضريبة القيمة المضافة";
            vatButton.Font = new Font("Segoe UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            vatButton.Click += VatButton_Click;

            // Add controls to form
            Controls.Add(titleLabel);
            Controls.Add(salesInvoicesButton);
            Controls.Add(purchasesInvoicesButton);
            Controls.Add(journalButton);
            Controls.Add(accountsButton);
            Controls.Add(financialReportsButton);
            Controls.Add(vatButton);

            // Form properties
            AutoScaleDimensions = new SizeF(15F, 37F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1307, 650);
            Name = "AccountingForm";
            Text = "نظام المحاسبة";
            StartPosition = FormStartPosition.CenterScreen;
            ResumeLayout(false);
            PerformLayout();
        }

        // Event handlers for the buttons
        private void SalesInvoicesButton_Click(object sender, EventArgs e)
        {
            MessageBox.Show("تم النقر على زر فواتير البيع");
        }

        private void PurchasesInvoicesButton_Click(object sender, EventArgs e)
        {
            MessageBox.Show("تم النقر على زر فواتير مشتريات");
        }

        private void JournalButton_Click(object sender, EventArgs e)
        {
            MessageBox.Show("تم النقر على زر دفتر اليومية");
        }

        private void AccountsButton_Click(object sender, EventArgs e)
        {
            MessageBox.Show("تم النقر على زر الحسابات");
        }

        private void FinancialReportsButton_Click(object sender, EventArgs e)
        {
            MessageBox.Show("تم النقر على زر التقارير المالية");
        }

        private void VatButton_Click(object sender, EventArgs e)
        {
            MessageBox.Show("تم النقر على زر ضريبة القيمة المضافة");
        }
    }
}