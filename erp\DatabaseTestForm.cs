using erp.Data;
using Serilog;

namespace erp
{
    /// <summary>
    /// نموذج اختبار الاتصال بقاعدة البيانات
    /// </summary>
    public partial class DatabaseTestForm : Form
    {
        private Button testConnectionButton = null!;
        private Button initializeDatabaseButton = null!;
        private TextBox logTextBox = null!;
        private Label statusLabel = null!;
        private ProgressBar progressBar = null!;

        public DatabaseTestForm()
        {
            InitializeComponent();
            InitializeLogger();
        }

        private void InitializeLogger()
        {
            Log.Logger = new LoggerConfiguration()
                .WriteTo.File("logs/database-test-.txt", rollingInterval: RollingInterval.Day)
                .CreateLogger();
        }

        private void InitializeComponent()
        {
            // إعداد النموذج
            this.Text = "اختبار قاعدة البيانات - نظام ERP";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;

            // زر اختبار الاتصال
            testConnectionButton = new Button();
            testConnectionButton.Text = "اختبار الاتصال";
            testConnectionButton.Size = new Size(150, 40);
            testConnectionButton.Location = new Point(50, 30);
            testConnectionButton.Click += TestConnectionButton_Click;
            this.Controls.Add(testConnectionButton);

            // زر تهيئة قاعدة البيانات
            initializeDatabaseButton = new Button();
            initializeDatabaseButton.Text = "تهيئة قاعدة البيانات";
            initializeDatabaseButton.Size = new Size(150, 40);
            initializeDatabaseButton.Location = new Point(220, 30);
            initializeDatabaseButton.Click += InitializeDatabaseButton_Click;
            this.Controls.Add(initializeDatabaseButton);

            // تسمية الحالة
            statusLabel = new Label();
            statusLabel.Text = "جاهز للاختبار";
            statusLabel.Size = new Size(300, 25);
            statusLabel.Location = new Point(50, 90);
            statusLabel.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            this.Controls.Add(statusLabel);

            // شريط التقدم
            progressBar = new ProgressBar();
            progressBar.Size = new Size(700, 25);
            progressBar.Location = new Point(50, 120);
            progressBar.Style = ProgressBarStyle.Marquee;
            progressBar.Visible = false;
            this.Controls.Add(progressBar);

            // مربع نص السجلات
            logTextBox = new TextBox();
            logTextBox.Multiline = true;
            logTextBox.ScrollBars = ScrollBars.Vertical;
            logTextBox.Size = new Size(700, 400);
            logTextBox.Location = new Point(50, 160);
            logTextBox.ReadOnly = true;
            logTextBox.Font = new Font("Consolas", 9F);
            this.Controls.Add(logTextBox);

            // إضافة رسالة ترحيب
            AppendLog("مرحباً بك في نظام اختبار قاعدة البيانات");
            AppendLog("يرجى التأكد من تشغيل خادم MySQL قبل البدء");
            AppendLog("الإعدادات الافتراضية: Server=localhost, Database=erp_system, User=root");
            AppendLog("----------------------------------------");
        }

        private async void TestConnectionButton_Click(object sender, EventArgs e)
        {
            try
            {
                SetUIState(false, "جاري اختبار الاتصال...");
                AppendLog("بدء اختبار الاتصال بقاعدة البيانات...");

                using var dbConnection = new DatabaseConnection();
                var isConnected = await dbConnection.TestConnectionAsync();

                if (isConnected)
                {
                    AppendLog("✓ نجح الاتصال بقاعدة البيانات!");
                    statusLabel.Text = "الاتصال ناجح";
                    statusLabel.ForeColor = Color.Green;
                }
                else
                {
                    AppendLog("✗ فشل الاتصال بقاعدة البيانات!");
                    statusLabel.Text = "فشل الاتصال";
                    statusLabel.ForeColor = Color.Red;
                }
            }
            catch (Exception ex)
            {
                AppendLog($"خطأ في اختبار الاتصال: {ex.Message}");
                statusLabel.Text = "خطأ في الاتصال";
                statusLabel.ForeColor = Color.Red;
                Log.Error("خطأ في اختبار الاتصال: {Error}", ex.Message);
            }
            finally
            {
                SetUIState(true, "جاهز");
            }
        }

        private async void InitializeDatabaseButton_Click(object sender, EventArgs e)
        {
            try
            {
                var result = MessageBox.Show(
                    "هل أنت متأكد من أنك تريد تهيئة قاعدة البيانات؟\nسيتم إنشاء الجداول والبيانات الأولية.",
                    "تأكيد التهيئة",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result != DialogResult.Yes)
                    return;

                SetUIState(false, "جاري تهيئة قاعدة البيانات...");
                AppendLog("بدء تهيئة قاعدة البيانات...");

                using var dbConnection = new DatabaseConnection();
                var initializer = new DatabaseInitializer(dbConnection);

                var success = await initializer.InitializeDatabaseAsync();

                if (success)
                {
                    AppendLog("✓ تم تهيئة قاعدة البيانات بنجاح!");
                    AppendLog("تم إنشاء جميع الجداول والبيانات الأولية");
                    AppendLog("المستخدم الافتراضي: admin / admin123");
                    statusLabel.Text = "تم التهيئة بنجاح";
                    statusLabel.ForeColor = Color.Green;

                    MessageBox.Show(
                        "تم تهيئة قاعدة البيانات بنجاح!\n\nالمستخدم الافتراضي:\nاسم المستخدم: admin\nكلمة المرور: admin123",
                        "نجح التهيئة",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information);
                }
                else
                {
                    AppendLog("✗ فشل في تهيئة قاعدة البيانات!");
                    statusLabel.Text = "فشل التهيئة";
                    statusLabel.ForeColor = Color.Red;

                    MessageBox.Show(
                        "فشل في تهيئة قاعدة البيانات!\nيرجى مراجعة ملف السجلات للحصول على تفاصيل أكثر.",
                        "خطأ في التهيئة",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                AppendLog($"خطأ في تهيئة قاعدة البيانات: {ex.Message}");
                statusLabel.Text = "خطأ في التهيئة";
                statusLabel.ForeColor = Color.Red;
                Log.Error("خطأ في تهيئة قاعدة البيانات: {Error}", ex.Message);

                MessageBox.Show(
                    $"حدث خطأ أثناء تهيئة قاعدة البيانات:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
            finally
            {
                SetUIState(true, "جاهز");
            }
        }

        private void SetUIState(bool enabled, string status)
        {
            testConnectionButton.Enabled = enabled;
            initializeDatabaseButton.Enabled = enabled;
            progressBar.Visible = !enabled;
            
            if (enabled)
            {
                statusLabel.Text = status;
                statusLabel.ForeColor = Color.Black;
            }
            else
            {
                statusLabel.Text = status;
                statusLabel.ForeColor = Color.Blue;
            }

            Application.DoEvents();
        }

        private void AppendLog(string message)
        {
            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            var logMessage = $"[{timestamp}] {message}";
            
            if (logTextBox.InvokeRequired)
            {
                logTextBox.Invoke(new Action(() =>
                {
                    logTextBox.AppendText(logMessage + Environment.NewLine);
                    logTextBox.ScrollToCaret();
                }));
            }
            else
            {
                logTextBox.AppendText(logMessage + Environment.NewLine);
                logTextBox.ScrollToCaret();
            }

            Log.Information(message);
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            Log.CloseAndFlush();
            base.OnFormClosing(e);
        }
    }
}
