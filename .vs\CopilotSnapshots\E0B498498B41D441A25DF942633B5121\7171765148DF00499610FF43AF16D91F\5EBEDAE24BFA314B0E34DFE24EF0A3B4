﻿namespace erp
{
    partial class Form1
    {
        /// <summary>
        ///  Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        private Panel sidebarPanel;
        private Button dashboardButton;
        private Button accountingSystemButton;
        private Button branchOperationsButton;

        /// <summary>
        ///  Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        ///  Required method for Designer support - do not modify
        ///  the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.sidebarPanel = new Panel();
            this.dashboardButton = new Button();
            this.accountingSystemButton = new Button();
            this.branchOperationsButton = new Button();

            // Sidebar Panel
            this.sidebarPanel.Dock = DockStyle.Left;
            this.sidebarPanel.Width = 200;
            this.sidebarPanel.BackColor = Color.LightGray;

            // Dashboard Button
            this.dashboardButton.Text = "لوحة القيادة";
            this.dashboardButton.Dock = DockStyle.Top;
            this.dashboardButton.Height = 50;

            // Accounting System Button
            this.accountingSystemButton.Text = "نظام المحاسبة";
            this.accountingSystemButton.Dock = DockStyle.Top;
            this.accountingSystemButton.Height = 50;

            // Branch Operations Button
            this.branchOperationsButton.Text = "إدارة عمليات الفروع";
            this.branchOperationsButton.Dock = DockStyle.Top;
            this.branchOperationsButton.Height = 50;

            // Add buttons to sidebar
            this.sidebarPanel.Controls.Add(this.branchOperationsButton);
            this.sidebarPanel.Controls.Add(this.accountingSystemButton);
            this.sidebarPanel.Controls.Add(this.dashboardButton);

            // Add sidebar to form
            this.Controls.Add(this.sidebarPanel);

            // Form properties
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(800, 450);
            this.Text = "الشاشة الرئيسية";
        }

        #endregion
    }
}
