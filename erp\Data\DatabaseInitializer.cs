using MySql.Data.MySqlClient;
using Serilog;

namespace erp.Data
{
    /// <summary>
    /// فئة تهيئة قاعدة البيانات وإنشاء الجداول والبيانات الأولية
    /// </summary>
    public class DatabaseInitializer
    {
        private readonly DatabaseConnection _dbConnection;
        private readonly string _scriptsPath;

        /// <summary>
        /// منشئ فئة تهيئة قاعدة البيانات
        /// </summary>
        /// <param name="dbConnection">كائن الاتصال بقاعدة البيانات</param>
        public DatabaseInitializer(DatabaseConnection dbConnection)
        {
            _dbConnection = dbConnection;
            _scriptsPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Scripts");
        }

        /// <summary>
        /// تهيئة قاعدة البيانات بالكامل
        /// </summary>
        /// <returns>true إذا تم التهيئة بنجاح</returns>
        public async Task<bool> InitializeDatabaseAsync()
        {
            try
            {
                Log.Information("بدء تهيئة قاعدة البيانات...");

                // 1. اختبار الاتصال
                if (!await _dbConnection.TestConnectionAsync())
                {
                    Log.Error("فشل في الاتصال بقاعدة البيانات");
                    return false;
                }

                // 2. إنشاء قاعدة البيانات إذا لم تكن موجودة
                if (!await CreateDatabaseIfNotExistsAsync())
                {
                    Log.Error("فشل في إنشاء قاعدة البيانات");
                    return false;
                }

                // 3. إنشاء الجداول
                if (!await CreateTablesAsync())
                {
                    Log.Error("فشل في إنشاء الجداول");
                    return false;
                }

                // 4. إدراج البيانات الأولية
                if (!await InsertInitialDataAsync())
                {
                    Log.Error("فشل في إدراج البيانات الأولية");
                    return false;
                }

                Log.Information("تم تهيئة قاعدة البيانات بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                Log.Error("خطأ في تهيئة قاعدة البيانات: {Error}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// إنشاء قاعدة البيانات إذا لم تكن موجودة
        /// </summary>
        /// <returns>true إذا تم الإنشاء بنجاح</returns>
        private async Task<bool> CreateDatabaseIfNotExistsAsync()
        {
            try
            {
                // الحصول على سلسلة الاتصال بدون اسم قاعدة البيانات
                var connectionString = GetConnectionStringWithoutDatabase();
                
                using var connection = new MySqlConnection(connectionString);
                await connection.OpenAsync();

                // التحقق من وجود قاعدة البيانات
                var checkQuery = "SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = 'erp_system'";
                using var checkCommand = new MySqlCommand(checkQuery, connection);
                var result = await checkCommand.ExecuteScalarAsync();

                if (result == null)
                {
                    // إنشاء قاعدة البيانات
                    var createQuery = "CREATE DATABASE `erp_system` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
                    using var createCommand = new MySqlCommand(createQuery, connection);
                    await createCommand.ExecuteNonQueryAsync();
                    
                    Log.Information("تم إنشاء قاعدة البيانات erp_system بنجاح");
                }
                else
                {
                    Log.Information("قاعدة البيانات erp_system موجودة بالفعل");
                }

                return true;
            }
            catch (Exception ex)
            {
                Log.Error("خطأ في إنشاء قاعدة البيانات: {Error}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// إنشاء الجداول من سكريبت SQL
        /// </summary>
        /// <returns>true إذا تم الإنشاء بنجاح</returns>
        private async Task<bool> CreateTablesAsync()
        {
            try
            {
                var scriptPath = Path.Combine(_scriptsPath, "CreateDatabase.sql");
                
                if (!File.Exists(scriptPath))
                {
                    Log.Error("ملف سكريبت إنشاء الجداول غير موجود: {ScriptPath}", scriptPath);
                    return false;
                }

                var script = await File.ReadAllTextAsync(scriptPath);
                
                // تقسيم السكريبت إلى عبارات منفصلة
                var statements = script.Split(new[] { ';' }, StringSplitOptions.RemoveEmptyEntries);

                var connection = await _dbConnection.OpenConnectionAsync();

                foreach (var statement in statements)
                {
                    var trimmedStatement = statement.Trim();
                    if (!string.IsNullOrEmpty(trimmedStatement) && 
                        !trimmedStatement.StartsWith("--") && 
                        !trimmedStatement.StartsWith("/*"))
                    {
                        try
                        {
                            using var command = new MySqlCommand(trimmedStatement, connection);
                            await command.ExecuteNonQueryAsync();
                        }
                        catch (MySqlException ex)
                        {
                            // تجاهل أخطاء "الجدول موجود بالفعل"
                            if (!ex.Message.Contains("already exists") && !ex.Message.Contains("Duplicate"))
                            {
                                Log.Warning("تحذير في تنفيذ العبارة: {Statement}, خطأ: {Error}", 
                                    trimmedStatement.Substring(0, Math.Min(50, trimmedStatement.Length)), ex.Message);
                            }
                        }
                    }
                }

                Log.Information("تم إنشاء الجداول بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                Log.Error("خطأ في إنشاء الجداول: {Error}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// إدراج البيانات الأولية
        /// </summary>
        /// <returns>true إذا تم الإدراج بنجاح</returns>
        private async Task<bool> InsertInitialDataAsync()
        {
            try
            {
                // التحقق من وجود المستخدم الافتراضي
                var userExists = await CheckIfUserExistsAsync("admin");
                
                if (!userExists)
                {
                    await CreateDefaultAdminUserAsync();
                }

                // التحقق من وجود الحسابات الأساسية
                var accountsExist = await CheckIfAccountsExistAsync();
                
                if (!accountsExist)
                {
                    await CreateDefaultAccountsAsync();
                }

                Log.Information("تم إدراج البيانات الأولية بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                Log.Error("خطأ في إدراج البيانات الأولية: {Error}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// التحقق من وجود مستخدم
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <returns>true إذا كان المستخدم موجوداً</returns>
        private async Task<bool> CheckIfUserExistsAsync(string username)
        {
            try
            {
                var query = "SELECT COUNT(*) FROM users WHERE username = @username";
                var parameters = new MySqlParameter[] { new MySqlParameter("@username", username) };
                
                var result = await _dbConnection.ExecuteScalarAsync(query, parameters);
                return Convert.ToInt32(result) > 0;
            }
            catch (Exception ex)
            {
                Log.Error("خطأ في التحقق من وجود المستخدم: {Error}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// إنشاء المستخدم الافتراضي
        /// </summary>
        /// <returns>true إذا تم الإنشاء بنجاح</returns>
        private async Task<bool> CreateDefaultAdminUserAsync()
        {
            try
            {
                // تشفير كلمة المرور الافتراضية
                var passwordHash = BCrypt.Net.BCrypt.HashPassword("admin123");
                
                var query = @"INSERT INTO users (username, password_hash, full_name, email, role, is_active) 
                             VALUES (@username, @password_hash, @full_name, @email, @role, @is_active)";
                
                var parameters = new MySqlParameter[]
                {
                    new MySqlParameter("@username", "admin"),
                    new MySqlParameter("@password_hash", passwordHash),
                    new MySqlParameter("@full_name", "مدير النظام"),
                    new MySqlParameter("@email", "<EMAIL>"),
                    new MySqlParameter("@role", "admin"),
                    new MySqlParameter("@is_active", true)
                };

                await _dbConnection.ExecuteNonQueryAsync(query, parameters);
                Log.Information("تم إنشاء المستخدم الافتراضي بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                Log.Error("خطأ في إنشاء المستخدم الافتراضي: {Error}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// التحقق من وجود الحسابات الأساسية
        /// </summary>
        /// <returns>true إذا كانت الحسابات موجودة</returns>
        private async Task<bool> CheckIfAccountsExistAsync()
        {
            try
            {
                var query = "SELECT COUNT(*) FROM accounts";
                var result = await _dbConnection.ExecuteScalarAsync(query);
                return Convert.ToInt32(result) > 0;
            }
            catch (Exception ex)
            {
                Log.Error("خطأ في التحقق من وجود الحسابات: {Error}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// إنشاء الحسابات الافتراضية
        /// </summary>
        /// <returns>true إذا تم الإنشاء بنجاح</returns>
        private async Task<bool> CreateDefaultAccountsAsync()
        {
            try
            {
                // قائمة الحسابات الأساسية
                var accounts = new[]
                {
                    new { Code = "1000", Name = "الأصول", Type = "asset", ParentId = (int?)null },
                    new { Code = "1100", Name = "الأصول المتداولة", Type = "asset", ParentId = (int?)1 },
                    new { Code = "1111", Name = "الصندوق", Type = "asset", ParentId = (int?)2 },
                    new { Code = "1112", Name = "البنك", Type = "asset", ParentId = (int?)2 },
                    new { Code = "1120", Name = "العملاء", Type = "asset", ParentId = (int?)2 },
                    new { Code = "2000", Name = "الخصوم", Type = "liability", ParentId = (int?)null },
                    new { Code = "2100", Name = "الخصوم المتداولة", Type = "liability", ParentId = (int?)6 },
                    new { Code = "2110", Name = "الموردون", Type = "liability", ParentId = (int?)7 },
                    new { Code = "3000", Name = "حقوق الملكية", Type = "equity", ParentId = (int?)null },
                    new { Code = "3100", Name = "رأس المال", Type = "equity", ParentId = (int?)9 },
                    new { Code = "4000", Name = "الإيرادات", Type = "revenue", ParentId = (int?)null },
                    new { Code = "4100", Name = "إيرادات المبيعات", Type = "revenue", ParentId = (int?)11 },
                    new { Code = "5000", Name = "المصروفات", Type = "expense", ParentId = (int?)null },
                    new { Code = "5100", Name = "تكلفة البضاعة المباعة", Type = "expense", ParentId = (int?)13 },
                    new { Code = "5200", Name = "مصروفات التشغيل", Type = "expense", ParentId = (int?)13 }
                };

                foreach (var account in accounts)
                {
                    var query = @"INSERT INTO accounts (account_code, account_name, account_type, parent_account_id, is_active) 
                                 VALUES (@code, @name, @type, @parent_id, @is_active)";
                    
                    var parameters = new MySqlParameter[]
                    {
                        new MySqlParameter("@code", account.Code),
                        new MySqlParameter("@name", account.Name),
                        new MySqlParameter("@type", account.Type),
                        new MySqlParameter("@parent_id", account.ParentId.HasValue ? (object)account.ParentId.Value : DBNull.Value),
                        new MySqlParameter("@is_active", true)
                    };

                    await _dbConnection.ExecuteNonQueryAsync(query, parameters);
                }

                Log.Information("تم إنشاء الحسابات الافتراضية بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                Log.Error("خطأ في إنشاء الحسابات الافتراضية: {Error}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// الحصول على سلسلة الاتصال بدون اسم قاعدة البيانات
        /// </summary>
        /// <returns>سلسلة الاتصال</returns>
        private string GetConnectionStringWithoutDatabase()
        {
            return "Server=localhost;Uid=root;Pwd=;Port=3306;Charset=utf8mb4;SslMode=none;";
        }
    }
}
