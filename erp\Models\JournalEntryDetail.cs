using System.ComponentModel.DataAnnotations;

namespace erp.Models
{
    /// <summary>
    /// نموذج تفصيل القيد المحاسبي
    /// </summary>
    public class JournalEntryDetail
    {
        /// <summary>
        /// معرف التفصيل
        /// </summary>
        public int DetailId { get; set; }

        /// <summary>
        /// معرف القيد
        /// </summary>
        [Required(ErrorMessage = "معرف القيد مطلوب")]
        public int EntryId { get; set; }

        /// <summary>
        /// القيد
        /// </summary>
        public JournalEntry? Entry { get; set; }

        /// <summary>
        /// معرف الحساب
        /// </summary>
        [Required(ErrorMessage = "الحساب مطلوب")]
        public int AccountId { get; set; }

        /// <summary>
        /// الحساب
        /// </summary>
        public Account? Account { get; set; }

        /// <summary>
        /// وصف التفصيل
        /// </summary>
        [StringLength(255, ErrorMessage = "وصف التفصيل يجب أن يكون أقل من 255 حرف")]
        public string? Description { get; set; }

        /// <summary>
        /// المبلغ المدين
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "المبلغ المدين يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal DebitAmount { get; set; } = 0;

        /// <summary>
        /// المبلغ الدائن
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "المبلغ الدائن يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal CreditAmount { get; set; } = 0;

        /// <summary>
        /// المبلغ الصافي (مدين - دائن)
        /// </summary>
        public decimal NetAmount => DebitAmount - CreditAmount;

        /// <summary>
        /// التحقق من كون التفصيل مدين
        /// </summary>
        public bool IsDebit => DebitAmount > 0 && CreditAmount == 0;

        /// <summary>
        /// التحقق من كون التفصيل دائن
        /// </summary>
        public bool IsCredit => CreditAmount > 0 && DebitAmount == 0;

        /// <summary>
        /// التحقق من صحة التفصيل
        /// </summary>
        public bool IsValid => (DebitAmount > 0 && CreditAmount == 0) || (CreditAmount > 0 && DebitAmount == 0);

        /// <summary>
        /// تعيين مبلغ مدين
        /// </summary>
        /// <param name="amount">المبلغ</param>
        public void SetDebitAmount(decimal amount)
        {
            if (amount < 0)
                throw new ArgumentException("المبلغ المدين يجب أن يكون أكبر من أو يساوي صفر");

            DebitAmount = amount;
            CreditAmount = 0;
        }

        /// <summary>
        /// تعيين مبلغ دائن
        /// </summary>
        /// <param name="amount">المبلغ</param>
        public void SetCreditAmount(decimal amount)
        {
            if (amount < 0)
                throw new ArgumentException("المبلغ الدائن يجب أن يكون أكبر من أو يساوي صفر");

            CreditAmount = amount;
            DebitAmount = 0;
        }

        /// <summary>
        /// تعيين المبلغ حسب طبيعة الحساب
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <param name="isIncrease">هل هو زيادة في الحساب</param>
        public void SetAmountByAccountNature(decimal amount, bool isIncrease)
        {
            if (Account == null)
                throw new InvalidOperationException("يجب تحديد الحساب أولاً");

            var accountNature = Account.GetAccountNature();

            if ((accountNature == AccountNature.Debit && isIncrease) || 
                (accountNature == AccountNature.Credit && !isIncrease))
            {
                SetDebitAmount(amount);
            }
            else
            {
                SetCreditAmount(amount);
            }
        }

        /// <summary>
        /// نسخ التفصيل
        /// </summary>
        /// <returns>نسخة من التفصيل</returns>
        public JournalEntryDetail Clone()
        {
            return new JournalEntryDetail
            {
                AccountId = AccountId,
                Description = Description,
                DebitAmount = DebitAmount,
                CreditAmount = CreditAmount
            };
        }

        /// <summary>
        /// التحقق من صحة التفصيل
        /// </summary>
        /// <returns>قائمة بالأخطاء</returns>
        public List<string> Validate()
        {
            var errors = new List<string>();

            if (AccountId <= 0)
                errors.Add("يجب تحديد الحساب");

            if (DebitAmount < 0)
                errors.Add("المبلغ المدين يجب أن يكون أكبر من أو يساوي صفر");

            if (CreditAmount < 0)
                errors.Add("المبلغ الدائن يجب أن يكون أكبر من أو يساوي صفر");

            if (DebitAmount == 0 && CreditAmount == 0)
                errors.Add("يجب إدخال مبلغ مدين أو دائن");

            if (DebitAmount > 0 && CreditAmount > 0)
                errors.Add("لا يمكن أن يكون التفصيل مدين ودائن في نفس الوقت");

            return errors;
        }

        /// <summary>
        /// الحصول على ملخص التفصيل
        /// </summary>
        /// <returns>ملخص التفصيل</returns>
        public string GetSummary()
        {
            var accountName = Account?.AccountName ?? "حساب غير محدد";
            var amount = DebitAmount > 0 ? DebitAmount : CreditAmount;
            var type = DebitAmount > 0 ? "مدين" : "دائن";
            
            var summary = $"{accountName} - {type}: {amount:C}";
            
            if (!string.IsNullOrEmpty(Description))
                summary += $" ({Description})";

            return summary;
        }

        /// <summary>
        /// الحصول على نوع الحركة
        /// </summary>
        /// <returns>نوع الحركة</returns>
        public string GetMovementType()
        {
            if (IsDebit) return "مدين";
            if (IsCredit) return "دائن";
            return "غير صحيح";
        }

        /// <summary>
        /// الحصول على المبلغ الفعلي
        /// </summary>
        /// <returns>المبلغ الفعلي</returns>
        public decimal GetAmount()
        {
            return DebitAmount > 0 ? DebitAmount : CreditAmount;
        }

        /// <summary>
        /// تحديث الوصف
        /// </summary>
        /// <param name="description">الوصف الجديد</param>
        public void UpdateDescription(string? description)
        {
            Description = description;
        }

        /// <summary>
        /// التحقق من تطابق طبيعة الحساب مع نوع الحركة
        /// </summary>
        /// <returns>true إذا كانت الحركة تزيد رصيد الحساب</returns>
        public bool IsIncreasingAccountBalance()
        {
            if (Account == null) return false;

            var accountNature = Account.GetAccountNature();
            
            // للحسابات ذات الطبيعة المدينة: المدين يزيد الرصيد
            if (accountNature == AccountNature.Debit)
                return IsDebit;
            
            // للحسابات ذات الطبيعة الدائنة: الدائن يزيد الرصيد
            return IsCredit;
        }

        /// <summary>
        /// الحصول على تأثير الحركة على رصيد الحساب
        /// </summary>
        /// <returns>مبلغ التأثير (موجب للزيادة، سالب للنقصان)</returns>
        public decimal GetBalanceImpact()
        {
            if (Account == null) return 0;

            var amount = GetAmount();
            var accountNature = Account.GetAccountNature();

            // للحسابات ذات الطبيعة المدينة
            if (accountNature == AccountNature.Debit)
            {
                return IsDebit ? amount : -amount;
            }
            
            // للحسابات ذات الطبيعة الدائنة
            return IsCredit ? amount : -amount;
        }
    }
}
