using System.ComponentModel.DataAnnotations;

namespace erp.Models
{
    /// <summary>
    /// نموذج المعاملة المالية
    /// </summary>
    public class FinancialTransaction
    {
        /// <summary>
        /// معرف المعاملة
        /// </summary>
        public int TransactionId { get; set; }

        /// <summary>
        /// نوع المعاملة
        /// </summary>
        [Required(ErrorMessage = "نوع المعاملة مطلوب")]
        public TransactionType TransactionType { get; set; }

        /// <summary>
        /// رقم المعاملة
        /// </summary>
        [Required(ErrorMessage = "رقم المعاملة مطلوب")]
        [StringLength(50, ErrorMessage = "رقم المعاملة يجب أن يكون أقل من 50 حرف")]
        public string TransactionNumber { get; set; } = string.Empty;

        /// <summary>
        /// معرف الحساب المرسل
        /// </summary>
        public int? FromAccountId { get; set; }

        /// <summary>
        /// الحساب المرسل
        /// </summary>
        public Account? FromAccount { get; set; }

        /// <summary>
        /// معرف الحساب المستقبل
        /// </summary>
        public int? ToAccountId { get; set; }

        /// <summary>
        /// الحساب المستقبل
        /// </summary>
        public Account? ToAccount { get; set; }

        /// <summary>
        /// المبلغ
        /// </summary>
        [Required(ErrorMessage = "المبلغ مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "المبلغ يجب أن يكون أكبر من صفر")]
        public decimal Amount { get; set; }

        /// <summary>
        /// وصف المعاملة
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// تاريخ المعاملة
        /// </summary>
        [Required(ErrorMessage = "تاريخ المعاملة مطلوب")]
        public DateTime TransactionDate { get; set; } = DateTime.Today;

        /// <summary>
        /// الرقم المرجعي
        /// </summary>
        [StringLength(100, ErrorMessage = "الرقم المرجعي يجب أن يكون أقل من 100 حرف")]
        public string? ReferenceNumber { get; set; }

        /// <summary>
        /// طريقة الدفع
        /// </summary>
        public PaymentMethod? PaymentMethod { get; set; }

        /// <summary>
        /// حالة المعاملة
        /// </summary>
        public TransactionStatus Status { get; set; } = TransactionStatus.Pending;

        /// <summary>
        /// معرف المنشئ
        /// </summary>
        [Required(ErrorMessage = "منشئ المعاملة مطلوب")]
        public int CreatedBy { get; set; }

        /// <summary>
        /// المنشئ
        /// </summary>
        public User? Creator { get; set; }

        /// <summary>
        /// معرف المعتمد
        /// </summary>
        public int? ApprovedBy { get; set; }

        /// <summary>
        /// المعتمد
        /// </summary>
        public User? Approver { get; set; }

        /// <summary>
        /// تاريخ الاعتماد
        /// </summary>
        public DateTime? ApprovedAt { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ آخر تحديث
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// التحقق من كون المعاملة معتمدة
        /// </summary>
        public bool IsApproved => Status == TransactionStatus.Completed;

        /// <summary>
        /// التحقق من كون المعاملة ملغية
        /// </summary>
        public bool IsCancelled => Status == TransactionStatus.Cancelled;

        /// <summary>
        /// التحقق من كون المعاملة معلقة
        /// </summary>
        public bool IsPending => Status == TransactionStatus.Pending;

        /// <summary>
        /// اعتماد المعاملة
        /// </summary>
        /// <param name="approvedBy">معرف المعتمد</param>
        public void Approve(int approvedBy)
        {
            if (Status != TransactionStatus.Pending)
                throw new InvalidOperationException("يمكن اعتماد المعاملات المعلقة فقط");

            Status = TransactionStatus.Completed;
            ApprovedBy = approvedBy;
            ApprovedAt = DateTime.Now;
            UpdatedAt = DateTime.Now;
        }

        /// <summary>
        /// إلغاء المعاملة
        /// </summary>
        public void Cancel()
        {
            if (Status == TransactionStatus.Completed)
                throw new InvalidOperationException("لا يمكن إلغاء معاملة معتمدة");

            Status = TransactionStatus.Cancelled;
            UpdatedAt = DateTime.Now;
        }

        /// <summary>
        /// إنشاء رقم معاملة تلقائي
        /// </summary>
        /// <param name="prefix">البادئة</param>
        /// <param name="sequence">الرقم التسلسلي</param>
        /// <returns>رقم المعاملة</returns>
        public static string GenerateTransactionNumber(string prefix, int sequence)
        {
            var year = DateTime.Now.Year;
            var month = DateTime.Now.Month;
            return $"{prefix}-{year:0000}-{month:00}-{sequence:0000}";
        }

        /// <summary>
        /// التحقق من صحة المعاملة
        /// </summary>
        /// <returns>قائمة بالأخطاء</returns>
        public List<string> Validate()
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(TransactionNumber))
                errors.Add("رقم المعاملة مطلوب");

            if (Amount <= 0)
                errors.Add("المبلغ يجب أن يكون أكبر من صفر");

            if (TransactionDate > DateTime.Today)
                errors.Add("تاريخ المعاملة لا يمكن أن يكون في المستقبل");

            // التحقق من الحسابات حسب نوع المعاملة
            switch (TransactionType)
            {
                case TransactionType.Payment:
                    if (!FromAccountId.HasValue)
                        errors.Add("حساب المرسل مطلوب للدفعات");
                    break;

                case TransactionType.Receipt:
                    if (!ToAccountId.HasValue)
                        errors.Add("حساب المستقبل مطلوب للإيصالات");
                    break;

                case TransactionType.Transfer:
                    if (!FromAccountId.HasValue)
                        errors.Add("حساب المرسل مطلوب للتحويلات");
                    if (!ToAccountId.HasValue)
                        errors.Add("حساب المستقبل مطلوب للتحويلات");
                    if (FromAccountId == ToAccountId)
                        errors.Add("لا يمكن أن يكون حساب المرسل والمستقبل نفس الحساب");
                    break;

                case TransactionType.Adjustment:
                    if (!FromAccountId.HasValue && !ToAccountId.HasValue)
                        errors.Add("يجب تحديد حساب واحد على الأقل للتسويات");
                    break;
            }

            return errors;
        }

        /// <summary>
        /// الحصول على وصف نوع المعاملة
        /// </summary>
        /// <returns>وصف نوع المعاملة</returns>
        public string GetTransactionTypeDescription()
        {
            return TransactionType switch
            {
                TransactionType.Payment => "دفعة",
                TransactionType.Receipt => "إيصال",
                TransactionType.Transfer => "تحويل",
                TransactionType.Adjustment => "تسوية",
                _ => "غير محدد"
            };
        }

        /// <summary>
        /// الحصول على وصف حالة المعاملة
        /// </summary>
        /// <returns>وصف حالة المعاملة</returns>
        public string GetStatusDescription()
        {
            return Status switch
            {
                TransactionStatus.Pending => "معلقة",
                TransactionStatus.Completed => "مكتملة",
                TransactionStatus.Cancelled => "ملغية",
                _ => "غير محدد"
            };
        }

        /// <summary>
        /// الحصول على وصف طريقة الدفع
        /// </summary>
        /// <returns>وصف طريقة الدفع</returns>
        public string GetPaymentMethodDescription()
        {
            return PaymentMethod switch
            {
                Models.PaymentMethod.Cash => "نقداً",
                Models.PaymentMethod.BankTransfer => "تحويل بنكي",
                Models.PaymentMethod.Check => "شيك",
                Models.PaymentMethod.CreditCard => "بطاقة ائتمان",
                Models.PaymentMethod.Other => "أخرى",
                null => "غير محدد",
                _ => "غير محدد"
            };
        }

        /// <summary>
        /// الحصول على ملخص المعاملة
        /// </summary>
        /// <returns>ملخص المعاملة</returns>
        public string GetSummary()
        {
            var summary = $"{GetTransactionTypeDescription()} - {Amount:C}";

            if (!string.IsNullOrEmpty(Description))
                summary += $" ({Description})";

            if (FromAccount != null && ToAccount != null)
                summary += $" من {FromAccount.AccountName} إلى {ToAccount.AccountName}";
            else if (FromAccount != null)
                summary += $" من {FromAccount.AccountName}";
            else if (ToAccount != null)
                summary += $" إلى {ToAccount.AccountName}";

            return summary;
        }

        /// <summary>
        /// نسخ المعاملة
        /// </summary>
        /// <returns>نسخة من المعاملة</returns>
        public FinancialTransaction Clone()
        {
            return new FinancialTransaction
            {
                TransactionType = TransactionType,
                FromAccountId = FromAccountId,
                ToAccountId = ToAccountId,
                Amount = Amount,
                Description = Description,
                TransactionDate = TransactionDate,
                PaymentMethod = PaymentMethod,
                CreatedBy = CreatedBy
            };
        }
    }

    /// <summary>
    /// أنواع المعاملات المالية
    /// </summary>
    public enum TransactionType
    {
        /// <summary>
        /// دفعة
        /// </summary>
        Payment,

        /// <summary>
        /// إيصال
        /// </summary>
        Receipt,

        /// <summary>
        /// تحويل
        /// </summary>
        Transfer,

        /// <summary>
        /// تسوية
        /// </summary>
        Adjustment
    }

    /// <summary>
    /// حالات المعاملة
    /// </summary>
    public enum TransactionStatus
    {
        /// <summary>
        /// معلقة
        /// </summary>
        Pending,

        /// <summary>
        /// مكتملة
        /// </summary>
        Completed,

        /// <summary>
        /// ملغية
        /// </summary>
        Cancelled
    }

    /// <summary>
    /// طرق الدفع
    /// </summary>
    public enum PaymentMethod
    {
        /// <summary>
        /// نقداً
        /// </summary>
        Cash,

        /// <summary>
        /// تحويل بنكي
        /// </summary>
        BankTransfer,

        /// <summary>
        /// شيك
        /// </summary>
        Check,

        /// <summary>
        /// بطاقة ائتمان
        /// </summary>
        CreditCard,

        /// <summary>
        /// أخرى
        /// </summary>
        Other
    }
}
