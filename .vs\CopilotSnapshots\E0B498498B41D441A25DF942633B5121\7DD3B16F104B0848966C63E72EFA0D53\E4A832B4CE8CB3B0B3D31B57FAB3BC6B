﻿namespace erp
{
    partial class Form1
    {
        /// <summary>
        ///  Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        private Button dashboardButton;
        private Button accountingSystemButton;
        private Button branchOperationsButton;

        /// <summary>
        ///  Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        ///  Required method for Designer support - do not modify
        ///  the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.dashboardButton = new Button();
            this.accountingSystemButton = new Button();
            this.branchOperationsButton = new Button();

            // Dashboard Button
            this.dashboardButton.Text = "لوحة القيادة";
            this.dashboardButton.Location = new Point(20, 20);
            this.dashboardButton.Size = new Size(150, 40);

            // Accounting System Button
            this.accountingSystemButton.Text = "نظام المحاسبة";
            this.accountingSystemButton.Location = new Point(20, 70);
            this.accountingSystemButton.Size = new Size(150, 40);
            this.accountingSystemButton.Click += new EventHandler(this.accountingSystemButton_Click);

            // Branch Operations Button
            this.branchOperationsButton.Text = "إدارة عمليات";
            this.branchOperationsButton.Location = new Point(20, 120);
            this.branchOperationsButton.Size = new Size(150, 40);

            // Add buttons to form
            this.Controls.Add(this.dashboardButton);
            this.Controls.Add(this.accountingSystemButton);
            this.Controls.Add(this.branchOperationsButton);

            // Form properties
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(800, 450);
            this.Text = "الشاشة الرئيسية";
        }

        private void AccountingSystemIcon_Click(object sender, EventArgs e)
        {
            // Navigate to Accounting System page
            Form accountingForm = new AccountingForm();
            accountingForm.Show();
            this.Hide();
        }

        #endregion
    }
}
